'use server'
import { paypalClient, PAYPAL_PLANS, PayPalPlanType } from './config';
import { database } from '@/lib/db/index';
import { subscriptions } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';

// Create PayPal subscription plan
export async function createPayPalPlan(planType: PayPalPlanType) {
  try {
    const plan = PAYPAL_PLANS[planType];
    
    const request = {
      body: {
        productId: process.env.PAYPAL_PRODUCT_ID!, // You need to create a product in PayPal dashboard
        name: plan.name,
        description: plan.description,
        status: 'ACTIVE',
        billingCycles: [
          {
            frequencyInterval: 1,
            frequency: plan.interval,
            tenureType: 'REGULAR',
            sequence: 1,
            totalCycles: 0, // 0 means infinite
            pricingScheme: {
              fixedPrice: {
                value: plan.price,
                currencyCode: plan.currency,
              },
            },
          },
        ],
        paymentPreferences: {
          autoBillOutstanding: true,
          setupFeeFailureAction: 'CONTINUE',
          paymentFailureThreshold: 3,
        },
        taxes: {
          percentage: '0',
          inclusive: false,
        },
      },
    };

    const response = await paypalClient.subscriptionsController.plansCreate(request);
    return {
      success: true,
      planId: response.result.id,
      data: response.result,
    };
  } catch (error) {
    console.error('Error creating PayPal plan:', error);
    return {
      success: false,
      error: 'Failed to create PayPal plan',
    };
  }
}

// Create PayPal subscription
export async function createPayPalSubscription(userId: string, planId: string, planType: PayPalPlanType) {
  try {
    const request = {
      body: {
        planId: planId,
        startTime: new Date(Date.now() + 60000).toISOString(), // Start 1 minute from now
        subscriber: {
          name: {
            givenName: 'User',
            surname: 'Name',
          },
        },
        applicationContext: {
          brandName: 'Your SaaS App',
          locale: 'en-US',
          shippingPreference: 'NO_SHIPPING',
          userAction: 'SUBSCRIBE_NOW',
          paymentMethod: {
            payerSelected: 'PAYPAL',
            payeePreferred: 'IMMEDIATE_PAYMENT_REQUIRED',
          },
          returnUrl: `${process.env.NEXTAUTH_URL}/api/paypal/subscription/success`,
          cancelUrl: `${process.env.NEXTAUTH_URL}/api/paypal/subscription/cancel`,
        },
      },
    };

    const response = await paypalClient.subscriptionsController.subscriptionsCreate(request);
    
    if (response.result.id) {
      // Create subscription record in database
      const plan = PAYPAL_PLANS[planType];
      await database.insert(subscriptions).values({
        id: createId(),
        userId: userId,
        type: 'PREMIUM',
        status: 'INACTIVE', // Will be updated when payment is confirmed
        creditsGrantedPerMonth: plan.credits,
        creditsRemaining: plan.credits,
        paypalSubscriptionId: response.result.id,
        paypalPlanId: planId,
        paymentProvider: 'paypal',
      });
    }

    return {
      success: true,
      subscriptionId: response.result.id,
      approvalUrl: response.result.links?.find(link => link.rel === 'approve')?.href,
      data: response.result,
    };
  } catch (error) {
    console.error('Error creating PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to create PayPal subscription',
    };
  }
}

// Get PayPal subscription details
export async function getPayPalSubscription(subscriptionId: string) {
  try {
    const response = await paypalClient.subscriptionsController.subscriptionsGet({
      subscriptionId: subscriptionId,
    });

    return {
      success: true,
      data: response.result,
    };
  } catch (error) {
    console.error('Error getting PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to get PayPal subscription',
    };
  }
}

// Cancel PayPal subscription
export async function cancelPayPalSubscription(subscriptionId: string, reason: string = 'User requested cancellation') {
  try {
    const request = {
      subscriptionId: subscriptionId,
      body: {
        reason: reason,
      },
    };

    const response = await paypalClient.subscriptionsController.subscriptionsCancel(request);
    
    // Update subscription status in database
    await database.update(subscriptions)
      .set({ 
        status: 'CANCELED',
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    return {
      success: true,
      data: response.result,
    };
  } catch (error) {
    console.error('Error canceling PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to cancel PayPal subscription',
    };
  }
}

// Activate PayPal subscription (called after successful payment)
export async function activatePayPalSubscription(subscriptionId: string) {
  try {
    const plan = PAYPAL_PLANS.PREMIUM; // Default to premium, you might want to get this from the subscription
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());

    await database.update(subscriptions)
      .set({
        status: 'ACTIVE',
        currentPeriodStart: now,
        currentPeriodEnd: nextMonth,
        updatedAt: now,
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error activating PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to activate PayPal subscription',
    };
  }
}
