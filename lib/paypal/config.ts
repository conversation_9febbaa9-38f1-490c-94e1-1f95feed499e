'use server'
import { PayPalApi, Environment } from '@paypal/paypal-server-sdk';

// PayPal configuration
const environment = process.env.NODE_ENV === 'production' 
  ? Environment.Live 
  : Environment.Sandbox;

const clientId = process.env.NODE_ENV === 'production'
  ? process.env.PAYPAL_CLIENT_ID!
  : process.env.PAYPAL_SANDBOX_CLIENT_ID!;

const clientSecret = process.env.NODE_ENV === 'production'
  ? process.env.PAYPAL_CLIENT_SECRET!
  : process.env.PAYPAL_SANDBOX_CLIENT_SECRET!;

// Initialize PayPal client
export const paypalClient = new PayPalApi({
  clientCredentialsAuthCredentials: {
    oAuthClientId: clientId,
    oAuthClientSecret: clientSecret,
  },
  environment: environment,
});

// PayPal subscription plans configuration
export const PAYPAL_PLANS = {
  PREMIUM: {
    name: 'Premium Plan',
    description: 'Premium subscription with 1000 credits per month',
    price: '9.99',
    currency: 'USD',
    interval: 'MONTH',
    credits: 1000,
  },
  PRO: {
    name: 'Pro Plan', 
    description: 'Pro subscription with 5000 credits per month',
    price: '29.99',
    currency: 'USD',
    interval: 'MONTH',
    credits: 5000,
  }
} as const;

export type PayPalPlanType = keyof typeof PAYPAL_PLANS;
