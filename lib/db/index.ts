import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Disable prefetch as it is not supported for "Transaction" pool mode
const client = postgres(process.env.DATABASE_URL!, { prepare: false });
export const db = drizzle(client, { schema });

// Global for development to prevent exhausting database connections
const globalForDb = globalThis as unknown as {
  db: typeof db;
};

export const database = globalForDb.db ?? db;

if (process.env.NODE_ENV !== 'production') globalForDb.db = database;
