import { pgTable, text, timestamp, boolean, integer, json, pgEnum, unique } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const subscriptionStatusEnum = pgEnum('subscription_status', [
  'ACTIVE',
  'CANCELED', 
  'INACTIVE',
  'PAST_DUE',
  'TRIALING'
]);

export const subscriptionTypeEnum = pgEnum('subscription_type', [
  'FREE',
  'PREMIUM'
]);

export const generationStatusEnum = pgEnum('generation_status', [
  'PENDING',
  'COMPLETED',
  'FAILED'
]);

export const modelCategoryEnum = pgEnum('model_category', [
  'TEXT_TO_IMAGE',
  'IMAGE_TO_IMAGE', 
  'IMAGE_ENHANCEMENT',
  'IMAGE_TO_TEXT',
  'TEXT_TO_VIDEO',
  'IMAGE_TO_VIDEO',
  'VIDEO_TO_VIDEO'
]);

// Tables
export const users = pgTable('User', {
  id: text('id').primaryKey(),
  name: text('name'),
  email: text('email').notNull().unique(),
  passwordHash: text('passwordHash'),
  emailVerified: timestamp('emailVerified'),
  image: text('image'),
  isActive: boolean('isActive').default(true).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const accounts = pgTable('Account', {
  userId: text('userId').notNull(),
  type: text('type').notNull(),
  provider: text('provider').notNull(),
  providerAccountId: text('providerAccountId').notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  expires_at: integer('expires_at'),
  token_type: text('token_type'),
  scope: text('scope'),
  id_token: text('id_token'),
  session_state: text('session_state'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
}, (table) => ({
  pk: unique().on(table.provider, table.providerAccountId),
}));

export const sessions = pgTable('Session', {
  sessionToken: text('sessionToken').primaryKey(),
  userId: text('userId').notNull(),
  expires: timestamp('expires').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const verificationTokens = pgTable('VerificationToken', {
  identifier: text('identifier').notNull(),
  token: text('token').notNull(),
  expires: timestamp('expires').notNull(),
}, (table) => ({
  pk: unique().on(table.identifier, table.token),
}));

export const authenticators = pgTable('Authenticator', {
  credentialID: text('credentialID').notNull().unique(),
  userId: text('userId').notNull(),
  providerAccountId: text('providerAccountId').notNull(),
  credentialPublicKey: text('credentialPublicKey').notNull(),
  counter: integer('counter').notNull(),
  credentialDeviceType: text('credentialDeviceType').notNull(),
  credentialBackedUp: boolean('credentialBackedUp').notNull(),
  transports: text('transports'),
}, (table) => ({
  pk: unique().on(table.userId, table.credentialID),
}));

export const subscriptions = pgTable('Subscription', {
  id: text('id').primaryKey(),
  userId: text('userId').notNull().unique(),
  type: subscriptionTypeEnum('type').notNull(),
  status: subscriptionStatusEnum('status').default('INACTIVE').notNull(),
  creditsGrantedPerMonth: integer('creditsGrantedPerMonth').default(0).notNull(),
  creditsRemaining: integer('creditsRemaining').default(0).notNull(),
  currentPeriodStart: timestamp('currentPeriodStart'),
  currentPeriodEnd: timestamp('currentPeriodEnd'),
  trialEndsAt: timestamp('trialEndsAt'),
  // Stripe fields
  stripeSubscriptionId: text('stripeSubscriptionId').unique(),
  stripeCustomerId: text('stripeCustomerId').unique(),
  stripePaymentMethodId: text('stripePaymentMethodId'),
  stripePriceId: text('stripePriceId'),
  // PayPal fields
  paypalSubscriptionId: text('paypalSubscriptionId').unique(),
  paypalCustomerId: text('paypalCustomerId').unique(),
  paypalPlanId: text('paypalPlanId'),
  paymentProvider: text('paymentProvider').default('stripe'), // 'stripe' or 'paypal'
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const models = pgTable('Model', {
  id: text('id').primaryKey(),
  modelName: text('modelName').notNull(),
  modelId: text('modelId').notNull().unique(),
  category: modelCategoryEnum('category').notNull(),
  provider: text('provider').notNull(),
  description: text('description'),
  isAvailable: boolean('isAvailable').default(true).notNull(),
  creditCost: integer('creditCost').notNull(),
  parameters: json('parameters'),
});

export const imageGenerations = pgTable('ImageGeneration', {
  id: text('id').primaryKey(),
  userId: text('userId').notNull(),
  userPrompt: text('userPrompt').notNull(),
  prompt: text('prompt').notNull(),
  negativePrompt: text('negativePrompt'),
  taskId: text('taskId'),
  modelId: text('modelId').notNull(),
  parameters: json('parameters').notNull(),
  status: generationStatusEnum('status').default('PENDING').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  completedAt: timestamp('completedAt'),
  creditsUsed: integer('creditsUsed').notNull(),
  metadata: json('metadata'),
});

export const images = pgTable('Image', {
  id: text('id').primaryKey(),
  generationId: text('generationId').notNull(),
  userId: text('userId').notNull(),
  imageUrl: text('imageUrl').notNull(),
  thumbnailUrl: text('thumbnailUrl'),
  storagePath: text('storagePath'),
  isFavorite: boolean('isFavorite').default(false).notNull(),
});

// Relations
export const usersRelations = relations(users, ({ one, many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  authenticators: many(authenticators),
  subscription: one(subscriptions),
  imageGenerations: many(imageGenerations),
  images: many(images),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const authenticatorsRelations = relations(authenticators, ({ one }) => ({
  user: one(users, {
    fields: [authenticators.userId],
    references: [users.id],
  }),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  user: one(users, {
    fields: [subscriptions.userId],
    references: [users.id],
  }),
}));

export const imageGenerationsRelations = relations(imageGenerations, ({ one, many }) => ({
  user: one(users, {
    fields: [imageGenerations.userId],
    references: [users.id],
  }),
  model: one(models, {
    fields: [imageGenerations.modelId],
    references: [models.id],
  }),
  images: many(images),
}));

export const imagesRelations = relations(images, ({ one }) => ({
  user: one(users, {
    fields: [images.userId],
    references: [users.id],
  }),
  generation: one(imageGenerations, {
    fields: [images.generationId],
    references: [imageGenerations.id],
  }),
}));

export const modelsRelations = relations(models, ({ many }) => ({
  generations: many(imageGenerations),
}));

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;

export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;

export type VerificationToken = typeof verificationTokens.$inferSelect;
export type NewVerificationToken = typeof verificationTokens.$inferInsert;

export type Authenticator = typeof authenticators.$inferSelect;
export type NewAuthenticator = typeof authenticators.$inferInsert;

export type Subscription = typeof subscriptions.$inferSelect;
export type NewSubscription = typeof subscriptions.$inferInsert;

export type Model = typeof models.$inferSelect;
export type NewModel = typeof models.$inferInsert;

export type ImageGeneration = typeof imageGenerations.$inferSelect;
export type NewImageGeneration = typeof imageGenerations.$inferInsert;

export type Image = typeof images.$inferSelect;
export type NewImage = typeof images.$inferInsert;

// Enum types
export type SubscriptionStatus = typeof subscriptionStatusEnum.enumValues[number];
export type SubscriptionType = typeof subscriptionTypeEnum.enumValues[number];
export type GenerationStatus = typeof generationStatusEnum.enumValues[number];
export type ModelCategory = typeof modelCategoryEnum.enumValues[number];
