'use server'

import AWS from 'aws-sdk';

AWS.config.update({
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  });

// use aws ses to send email
export const sendEmail = async (email: string, subject: string, message: string) => {
    
    if (!process.env.AWS_SES_SOURCE_EMAIL) {
        throw new Error('AWS_SES_SOURCE_EMAIL environment variable is not set');
    }
    
    const ses = new AWS.SES({apiVersion: '2010-12-01'});
    const params = {
      Source: process.env.AWS_SES_SOURCE_EMAIL,
      Destination: {
        ToAddresses: [email],
        CcAddresses: [],
        BccAddresses: []
      },
      Message: {
        Body: {
          Text: {
            Data: message
          }
        },
        Subject: {
          Data: subject
        }
      }
    };
    try {
      const data = await ses.sendEmail(params).promise();
      return { success: true, messageId: data.MessageId };
    } catch (err) {
      if (err instanceof Error) {
        console.error('Error sending email:', err.message);
        console.error('Stack trace:', err.stack);
        return { success: false, error: err.message };
      }
      console.error('An unknown error occurred while sending email');
      return { success: false, error: 'An unknown error occurred' };
    }

}