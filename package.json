{"name": "saas-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx drizzle/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.810.0", "@marsidev/react-turnstile": "^1.1.0", "@paralleldrive/cuid2": "^2.2.2", "@paypal/paypal-server-sdk": "^1.1.0", "@paypal/react-paypal-js": "^8.8.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.36.4", "framer-motion": "^12.12.1", "jose": "^6.0.11", "lucide-react": "^0.483.0", "next": "15.3.2", "next-auth": "5.0.0-beta.28", "next-themes": "^0.4.6", "openai": "^4.98.0", "postgres": "^3.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-turnstile": "^1.1.4", "replicate": "^1.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.46", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "drizzle-kit": "^0.30.0", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "pnpm": {"overrides": {"@types/react": "19.1.6", "@types/react-dom": "19.1.5"}}}