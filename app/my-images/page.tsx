'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Download, Heart, ImageIcon, Loader2, Maximize2, X, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import { format } from 'date-fns'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'

// 定义图片类型
interface ImageItem {
  id: string
  imageUrl: string
  thumbnailUrl?: string | null
  isFavorite: boolean
  createdAt: Date
}

// 定义生成任务类型
interface GenerationItem {
  id: string
  userPrompt: string
  prompt: string
  negativePrompt?: string | null
  parameters: any
  createdAt: Date
  status: 'PENDING' | 'COMPLETED' | 'FAILED'
  model: {
    id: string
    modelName: string
  }
  images: ImageItem[]
  creditsUsed: number
}

// 图片查看对话框组件
function ImageViewDialog({ 
  isOpen, 
  onClose, 
  images,
  generationInfo
}: { 
  isOpen: boolean, 
  onClose: () => void, 
  images: ImageItem[],
  generationInfo: {
    userPrompt: string;
    createdAt: Date;
    modelName: string;
    parameters: any;
    negativePrompt?: string | null;
  }
}) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  
  // 处理图片下载
  const handleDownload = async (imageUrl: string, fileName: string) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('下载图片失败:', error)
    }
  }

  // 切换到下一张图片
  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length)
  }

  // 切换到上一张图片
  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="flex items-center justify-center max-w-none w-auto p-0 border-none bg-transparent shadow-none">
        <div className="relative flex flex-col md:flex-row w-[90vw] h-[90vh] bg-white rounded-lg overflow-hidden">
          {/* 图片区域 */}
          <div className="relative flex-1 bg-black flex items-center justify-center min-h-[50vh] md:min-h-0">
            {images.length > 0 ? (
              <>
                <Image
                  src={images[selectedImageIndex].imageUrl}
                  alt={generationInfo.userPrompt}
                  fill
                  className="object-contain"
                  priority
                />
                
                {/* 导航按钮 - 仅当有多张图片时显示 */}
                {images.length > 1 && (
                  <>
                    <button 
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                      onClick={prevImage}
                    >
                      <X className="h-6 w-6 text-white transform rotate-45" />
                    </button>
                    <button 
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                      onClick={nextImage}
                    >
                      <X className="h-6 w-6 text-white transform -rotate-45" />
                    </button>
                    
                    {/* 图片计数器 */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 px-3 py-1 rounded-full text-white text-sm">
                      {selectedImageIndex + 1} / {images.length}
                    </div>
                  </>
                )}
              </>
            ) : (
              <div className="text-white">没有可用的图片</div>
            )}
            
            {/* 关闭按钮 */}
            <button 
              className="absolute top-4 right-4 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
              onClick={onClose}
            >
              <X className="h-6 w-6 text-white" />
            </button>
            
            {/* 下载按钮 */}
            {images.length > 0 && (
              <button 
                className="absolute bottom-4 right-4 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                onClick={() => handleDownload(
                  images[selectedImageIndex].imageUrl, 
                  `image-${images[selectedImageIndex].id.substring(0, 8)}.png`
                )}
              >
                <Download className="h-6 w-6 text-white" />
              </button>
            )}
          </div>
          
          {/* 信息区域 */}
          <div className="w-full md:w-[350px] p-6 overflow-y-auto">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">生成信息</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">创建时间</p>
                    <p className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 mr-1" />
                      {format(new Date(generationInfo.createdAt), 'yyyy-MM-dd HH:mm:ss')}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">模型</p>
                    <p className="text-sm">{generationInfo.modelName}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">提示词</h3>
                <p className="text-sm bg-gray-50 p-3 rounded-md">{generationInfo.userPrompt}</p>
              </div>
              
              {generationInfo.negativePrompt && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">负面提示词</h3>
                  <p className="text-sm bg-gray-50 p-3 rounded-md">{generationInfo.negativePrompt}</p>
                </div>
              )}
              
              <div>
                <h3 className="text-lg font-semibold mb-2">参数</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm">
                    <p className="text-gray-500">宽度</p>
                    <p>{generationInfo.parameters.width}</p>
                  </div>
                  <div className="text-sm">
                    <p className="text-gray-500">高度</p>
                    <p>{generationInfo.parameters.height}</p>
                  </div>
                  {generationInfo.parameters.seed !== undefined && (
                    <div className="text-sm">
                      <p className="text-gray-500">种子</p>
                      <p>{generationInfo.parameters.seed}</p>
                    </div>
                  )}
                  {generationInfo.parameters.guidance !== undefined && (
                    <div className="text-sm">
                      <p className="text-gray-500">引导比例</p>
                      <p>{generationInfo.parameters.guidance}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// 生成任务卡片组件
function GenerationCard({ generation, onClick }: { 
  generation: GenerationItem, 
  onClick: () => void 
}) {
  // 获取状态图标和颜色
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return { icon: <CheckCircle className="h-4 w-4 text-green-500" />, text: '已完成', color: 'bg-green-100 text-green-800' }
      case 'PENDING':
        return { icon: <Clock className="h-4 w-4 text-amber-500" />, text: '处理中', color: 'bg-amber-100 text-amber-800' }
      case 'FAILED':
        return { icon: <AlertCircle className="h-4 w-4 text-red-500" />, text: '失败', color: 'bg-red-100 text-red-800' }
      default:
        return { icon: <Clock className="h-4 w-4 text-gray-500" />, text: '未知', color: 'bg-gray-100 text-gray-800' }
    }
  }

  const statusInfo = getStatusInfo(generation.status)
  const hasImages = generation.images && generation.images.length > 0
  const thumbnailImage = hasImages ? generation.images[0].imageUrl : null

  return (
    <Card className="overflow-hidden group cursor-pointer hover:shadow-md transition-shadow" onClick={onClick}>
      <div className="relative aspect-video bg-gray-100">
        {thumbnailImage ? (
          <Image
            src={thumbnailImage}
            alt={generation.userPrompt}
            fill
            className="object-cover transition-transform group-hover:scale-105"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <ImageIcon className="h-12 w-12 text-gray-300" />
          </div>
        )}
        
        {/* 状态标签 */}
        <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium flex items-center ${statusInfo.color}`}>
          {statusInfo.icon}
          <span className="ml-1">{statusInfo.text}</span>
        </div>
        
        {/* 图片数量标签 */}
        {hasImages && (
          <div className="absolute bottom-2 left-2 px-2 py-1 rounded-full bg-black/50 text-white text-xs font-medium">
            {generation.images.length} 张图片
          </div>
        )}
      </div>
      
      <CardContent className="p-4">
        <div className="line-clamp-2 text-sm font-medium mb-1" title={generation.userPrompt}>
          {generation.userPrompt}
        </div>
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span className="flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {format(new Date(generation.createdAt), 'yyyy-MM-dd')}
          </span>
          <span>{generation.model.modelName}</span>
        </div>
      </CardContent>
    </Card>
  )
}

// 骨架屏组件
function GenerationCardSkeleton() {
  return (
    <div className="overflow-hidden rounded-lg border">
      <Skeleton className="aspect-video" />
      <div className="p-4 space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <div className="flex justify-between pt-2">
          <Skeleton className="h-3 w-1/3" />
          <Skeleton className="h-3 w-1/4" />
        </div>
      </div>
    </div>
  )
}

export default function MyImagesPage() {
  const router = useRouter()
  const [generations, setGenerations] = useState<GenerationItem[]>([])
  const [favoriteGenerations, setFavoriteGenerations] = useState<GenerationItem[]>([])
  const [selectedGeneration, setSelectedGeneration] = useState<GenerationItem | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')

  // 获取用户图片生成记录
  useEffect(() => {
    const fetchGenerations = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/user/images')
        if (!response.ok) throw new Error('获取生成记录失败')
        
        const data = await response.json()
        setGenerations(data.generations || [])
        
        // 筛选出有收藏图片的生成记录
        const withFavorites = data.generations.filter((gen: GenerationItem) => 
          gen.images.some(img => img.isFavorite)
        )
        setFavoriteGenerations(withFavorites)
      } catch (error) {
        console.error('获取生成记录失败:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchGenerations()
  }, [])

  // 打开图片查看对话框
  const openImageDialog = (generation: GenerationItem) => {
    setSelectedGeneration(generation)
    setIsDialogOpen(true)
  }

  // 渲染骨架屏
  const renderSkeletons = () => {
    return Array(6).fill(0).map((_, index) => (
      <GenerationCardSkeleton key={index} />
    ))
  }

  return (
    <div className="contianer mx-auto py-8 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">我的图片</h1>
          <p className="text-gray-500 mt-1">查看和管理您生成的所有图片</p>
        </div>
        
      </div>

      
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
              {renderSkeletons()}
            </div>
          ) : generations.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
              {generations.map(generation => (
                <GenerationCard 
                  key={generation.id} 
                  generation={generation} 
                  onClick={() => openImageDialog(generation)}
                />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <ImageIcon className="h-16 w-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium">没有找到生成记录</h3>
              <p className="text-gray-500 mt-1">您还没有生成任何图片</p>
              <Button className="mt-4" onClick={() => router.push('/')}>
                开始生成图片
              </Button>
            </div>
          )}
        
        
      {/* 图片查看对话框 */}
      {selectedGeneration && (
        <ImageViewDialog 
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          images={selectedGeneration.images}
          generationInfo={{
            userPrompt: selectedGeneration.userPrompt,
            createdAt: selectedGeneration.createdAt,
            modelName: selectedGeneration.model.modelName,
            parameters: selectedGeneration.parameters,
            negativePrompt: selectedGeneration.negativePrompt
          }}
        />
      )}
    </div>
  )
}
