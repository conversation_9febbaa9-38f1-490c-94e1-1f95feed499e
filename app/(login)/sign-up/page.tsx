'use client'

import { RegisterForm } from "@/components/RegisterForm";
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useEffect } from "react"

export default function SignUpPage() {
    const router = useRouter()
    const { data: session } = useSession()
    useEffect(()=>{
      if (session?.user) {
        router.push("/profile")
      }
    },[session])
    console.log('where am i?')
    return (
        <div className="flex flex-col gap-y-4 min-h-screen items-center justify-center px-4 py-12">
            <div className="text-2xl font-bold text-center">Sign Up</div>
            <RegisterForm />
        </div>
    );
}