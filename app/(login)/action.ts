'use server'
import { verifyTurnstile } from '@/lib/cf/turnstile'
import { database } from '@/lib/db/index'
import { users } from '@/lib/db/schema'
import { hash } from 'bcryptjs'
import { eq } from 'drizzle-orm'
import { createId } from '@paralleldrive/cuid2'
const SALT = 10
export async function signUp(prevState: unknown, formData: FormData) {
  // type-casting here for convenience
  // in practice, you should validate your inputs
  const userData = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }
  const token = formData.get('token') as string;
  //check token
  const checkRes = await verifyTurnstile(token);
  if (!checkRes) {
    return { status:'failed', error: new Error('Invalidturnstile token'), email: userData.email, password: userData.password, state:prevState}
  }
  
  const newUser = {
    email: userData.email,
    passwordHash: await hash(userData.password, SALT),
    isActive: true,
  }
  //根据邮箱判断用户是否已经存在
  const existingUserResult = await database.select().from(users).where(eq(users.email, userData.email)).limit(1);
  const existingUser = existingUserResult[0];
  if (existingUser) {
    return { status:'failed', error: new Error('User already exists'), email: userData.email, password: userData.password, state:prevState}
  }

  const userId = createId();
  const userWithId = { ...newUser, id: userId };
  await database.insert(users).values(userWithId);
  const user = { id: userId, email: userData.email };
  //set session
  return { status: 'ok', message: 'Sign up successful', user: { id: user.id, email: user.email } }
}
