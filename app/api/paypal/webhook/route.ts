import { NextRequest, NextResponse } from 'next/server';
import { database } from '@/lib/db/index';
import { subscriptions } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { PAYPAL_PLANS } from '@/lib/paypal/config';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const eventType = body.event_type;
    const resource = body.resource;

    console.log('PayPal Webhook received:', eventType);

    switch (eventType) {
      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await handleSubscriptionActivated(resource);
        break;
      
      case 'BILLING.SUBSCRIPTION.CANCELLED':
        await handleSubscriptionCancelled(resource);
        break;
      
      case 'BILLING.SUBSCRIPTION.SUSPENDED':
        await handleSubscriptionSuspended(resource);
        break;
      
      case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
        await handlePaymentFailed(resource);
        break;
      
      case 'PAYMENT.SALE.COMPLETED':
        await handlePaymentCompleted(resource);
        break;
      
      default:
        console.log('Unhandled PayPal webhook event:', eventType);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error processing PayPal webhook:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleSubscriptionActivated(resource: any) {
  try {
    const subscriptionId = resource.id;
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());

    await database.update(subscriptions)
      .set({
        status: 'ACTIVE',
        currentPeriodStart: now,
        currentPeriodEnd: nextMonth,
        updatedAt: now,
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    console.log('PayPal subscription activated:', subscriptionId);
  } catch (error) {
    console.error('Error handling subscription activation:', error);
  }
}

async function handleSubscriptionCancelled(resource: any) {
  try {
    const subscriptionId = resource.id;

    await database.update(subscriptions)
      .set({
        status: 'CANCELED',
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    console.log('PayPal subscription cancelled:', subscriptionId);
  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
  }
}

async function handleSubscriptionSuspended(resource: any) {
  try {
    const subscriptionId = resource.id;

    await database.update(subscriptions)
      .set({
        status: 'PAST_DUE',
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    console.log('PayPal subscription suspended:', subscriptionId);
  } catch (error) {
    console.error('Error handling subscription suspension:', error);
  }
}

async function handlePaymentFailed(resource: any) {
  try {
    const subscriptionId = resource.id;

    await database.update(subscriptions)
      .set({
        status: 'PAST_DUE',
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    console.log('PayPal payment failed for subscription:', subscriptionId);
  } catch (error) {
    console.error('Error handling payment failure:', error);
  }
}

async function handlePaymentCompleted(resource: any) {
  try {
    // This is called when a recurring payment is completed
    const subscriptionId = resource.billing_agreement_id;
    
    if (subscriptionId) {
      // Get subscription details
      const subscriptionResult = await database.select()
        .from(subscriptions)
        .where(eq(subscriptions.paypalSubscriptionId, subscriptionId))
        .limit(1);

      const subscription = subscriptionResult[0];
      
      if (subscription) {
        // Reset credits for the new billing period
        const planType = subscription.paypalPlanId?.includes('PRO') ? 'PRO' : 'PREMIUM';
        const plan = PAYPAL_PLANS[planType];
        
        const now = new Date();
        const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());

        await database.update(subscriptions)
          .set({
            creditsRemaining: plan.credits,
            currentPeriodStart: now,
            currentPeriodEnd: nextMonth,
            status: 'ACTIVE',
            updatedAt: now,
          })
          .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

        console.log('PayPal payment completed, credits reset for subscription:', subscriptionId);
      }
    }
  } catch (error) {
    console.error('Error handling payment completion:', error);
  }
}
