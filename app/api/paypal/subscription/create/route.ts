import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { createPayPalSubscription, createPayPalPlan, PayPalPlanType } from '@/lib/paypal/subscriptions';

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { planType } = await req.json();
    
    if (!planType || !['PREMIUM', 'PRO'].includes(planType)) {
      return NextResponse.json({ error: 'Invalid plan type' }, { status: 400 });
    }

    // First, create or get the PayPal plan
    const planResult = await createPayPalPlan(planType as PayPalPlanType);
    
    if (!planResult.success) {
      return NextResponse.json({ error: planResult.error }, { status: 500 });
    }

    // Then create the subscription
    const subscriptionResult = await createPayPalSubscription(
      session.user.id,
      planResult.planId!,
      planType as PayPalPlanType
    );

    if (!subscriptionResult.success) {
      return NextResponse.json({ error: subscriptionResult.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      subscriptionId: subscriptionResult.subscriptionId,
      approvalUrl: subscriptionResult.approvalUrl,
    });

  } catch (error) {
    console.error('Error creating PayPal subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
