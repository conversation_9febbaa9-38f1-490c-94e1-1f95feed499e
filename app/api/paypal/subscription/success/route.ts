import { NextRequest, NextResponse } from 'next/server';
import { activatePayPalSubscription } from '@/lib/paypal/subscriptions';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const subscriptionId = searchParams.get('subscription_id');
    const token = searchParams.get('token');

    if (!subscriptionId) {
      return NextResponse.redirect(new URL('/dashboard?error=missing_subscription_id', req.url));
    }

    // Activate the subscription
    const result = await activatePayPalSubscription(subscriptionId);

    if (!result.success) {
      return NextResponse.redirect(new URL('/dashboard?error=activation_failed', req.url));
    }

    // Redirect to success page
    return NextResponse.redirect(new URL('/dashboard?success=subscription_activated', req.url));

  } catch (error) {
    console.error('Error handling PayPal subscription success:', error);
    return NextResponse.redirect(new URL('/dashboard?error=internal_error', req.url));
  }
}
