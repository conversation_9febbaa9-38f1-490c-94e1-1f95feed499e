'use server'
import { NextRequest } from "next/server";
import { database } from "@/lib/db/index";
import { users, models, subscriptions, imageGenerations } from "@/lib/db/schema";
import { auth } from "@/auth";
import { enhanceUserPrompt } from "@/lib/ai/image";
import { eq, and } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export async function POST(req: NextRequest) {
  try {
    // Clone the request to read the body without consuming it
    const body = await req.json();

    // Get the current user session
    const session = await auth()
    if (!session?.user) {
      return new Response(JSON.stringify({status: 'failed', error: "Unauthorized" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Extract user ID from session
    const userId = session.user.id;
    const userResult = await database.select().from(users).where(eq(users.id, userId)).limit(1);
    const user = userResult[0];

    if (!user) {
      return new Response(JSON.stringify({status: 'failed', error: "User not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }

    // Extract parameters from the request body
    const { prompt, parameters, model_id } = body;
    
  //   // Find the model to get credit cost
    const modelResult = await database.select().from(models).where(and(eq(models.id, model_id), eq(models.isAvailable, true))).limit(1);
    const model = modelResult[0];

    if (!model) {
      return new Response(JSON.stringify({status: 'failed', error: "Model not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
  const userSubscriptionResult = await database.select().from(subscriptions).where(eq(subscriptions.userId, user.id)).limit(1);
  const userSubscription = userSubscriptionResult[0];
  if (!userSubscription) {
    return new Response(JSON.stringify({status: 'failed', error: "User subscription not found" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  //calculate the credit cost
  const creditCost = model.creditCost * parameters.num;
  if (userSubscription.creditsRemaining < creditCost) {
    return new Response(JSON.stringify({status: 'failed', error: "Not enough credits" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  //Enhance the user prompt
  const enhancePrompt = await enhanceUserPrompt(prompt);
  if (enhancePrompt.error) {
    return new Response(JSON.stringify({status: 'failed', error: enhancePrompt.error }), {
      headers: { "Content-Type": "application/json" }
    });
  }

  //update the user subscription
  await database.update(subscriptions)
    .set({ creditsRemaining: userSubscription.creditsRemaining - creditCost })
    .where(eq(subscriptions.userId, user.id));

  //   // Create image generation record
    const imageGenerationId = createId();
    await database.insert(imageGenerations).values({
      id: imageGenerationId,
      userId: user.id,
      userPrompt: prompt,
      prompt: enhancePrompt.prompt as string,
      modelId: model.id,
      parameters,
      status: "FAILED",
      creditsUsed: creditCost
    });
    const webhook = `${process.env.SITE_URL}/api/callback/fal`;

  //   // Add generation ID to the request headers to track it
   const responseApi = await fetch(`https://queue.fal.run/${model.modelId}?fal_webhook=${webhook}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Key ${process.env.FAL_KEY}`
      },
      body: JSON.stringify({
        prompt: enhancePrompt.prompt as string,
        num_images: parameters.num,
        image_size:{
          width: parameters.width,
          height: parameters.height
        },
        seed: parameters.seed,
        num_inference_steps: parameters.steps,
        sync_mode: false,
        enable_safety_checker: true,
        //guidance_scale: parameters.guidance
      })
    });
  const responseData = await responseApi.json();

  //   // Update the image generation record with the task ID
    if (responseData.request_id) {
      await database.update(imageGenerations)
        .set({ taskId: responseData.request_id, status: "PENDING", metadata: responseData })
        .where(eq(imageGenerations.id, imageGenerationId));
    } else {
      await database.update(imageGenerations)
        .set({ status: "FAILED", metadata: responseData })
        .where(eq(imageGenerations.id, imageGenerationId));
    }

  //   return response and user remaining credits
  const updatedCredits = userSubscription.creditsRemaining - creditCost;
  return new Response(JSON.stringify({ status: 'ok', taskId: imageGenerationId, credits: updatedCredits}));
  
  } catch (error) {
    console.error("Error in FAL proxy:", error);
    return new Response(JSON.stringify({status: 'failed', error: "Internal Server Error" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
}

