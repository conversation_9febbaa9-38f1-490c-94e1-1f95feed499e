import { database } from "@/lib/db/index";
import { models, ModelCategory } from "@/lib/db/schema";
import { NextRequest } from "next/server";
import { eq, and } from "drizzle-orm";

export async function GET(request: NextRequest) {
  const category = request.nextUrl.searchParams.get('c')
  if (!category) {
    return new Response(JSON.stringify([]));
  } 
  const modelsList = await database.select().from(models).where(and(eq(models.isAvailable, true), eq(models.category, category as ModelCategory)));

  return new Response(JSON.stringify(modelsList));
}
