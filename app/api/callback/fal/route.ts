'use server'
import { NextRequest, NextResponse } from "next/server";
import { database } from "@/lib/db/index";
import { imageGenerations, images } from "@/lib/db/schema";
import { uploadImageFromUrlToR2 } from "@/lib/storage";
import { eq } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      request_id,
      //gateway_request_id,
      status,
      payload,
      //error,
      payload_error
    } = body;

    // Log the webhook for debugging
    console.log("FAL Webhook received:", body);

    // Find the image generation record by request_id
    const imageGenerationResult = await database.select().from(imageGenerations).where(eq(imageGenerations.taskId, request_id)).limit(1);
    const imageGeneration = imageGenerationResult[0];

    if (!imageGeneration) {
      console.error("Image generation not found for task_id:", request_id);
      return NextResponse.json({ error: "Image generation not found" }, { status: 404 });
    }
    //check if the image generation is already completed
    if(imageGeneration.status === 'COMPLETED'){
      console.log('Image generation already completed:', request_id);
      return NextResponse.json({ success: true });
    }

    // Update the image generation record with status and error info
    await database.update(imageGenerations)
      .set({
        status: status === "OK" ? "COMPLETED" : "FAILED",
        completedAt: new Date()
      })
      .where(eq(imageGenerations.id, imageGeneration.id));

    // If successful and payload.images exists, create image records
    if (status === "OK" && payload && Array.isArray(payload.images)) {
      await Promise.allSettled(
        payload.images.map(async (img: any, index: number) => {
          const key = `${request_id}-${index}`;
          const imagePath = await uploadImageFromUrlToR2(key, img.url);
          if(imagePath){
            await database.insert(images).values({
              id: createId(),
              generationId: imageGeneration.id,
              imageUrl: img.url,
              thumbnailUrl: img.url, // You can update this if you have a real thumbnail
              storagePath: imagePath,
              userId: imageGeneration.userId
            });
          }
        })
      );
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error("Error processing FAL webhook:", err);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
