import { auth } from "@/auth"
import { NextResponse, type NextRequest } from "next/server"
import { database } from "@/lib/db/index"
import { users, subscriptions, images, imageGenerations } from "@/lib/db/schema"
import { eq } from "drizzle-orm"

export async function POST(req: NextRequest) {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ status: 'failed', error: 'Unauthorized' })
    }
    try{
        // Delete in order: images -> imageGenerations -> subscription -> user
        await database.delete(images).where(eq(images.userId, session.user.id));

        await database.delete(imageGenerations).where(eq(imageGenerations.userId, session.user.id));

        await database.delete(subscriptions).where(eq(subscriptions.userId, session.user.id));

        await database.delete(users).where(eq(users.id, session.user.id));
        return NextResponse.json({ status: 'ok', message: 'User deleted' });
    }catch(error){
      console.error("Error deleting user:", error);
      return NextResponse.json({ status: 'failed', error: 'Internal Server Error' })
    }
}
