import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { database } from '@/lib/db/index';
import { imageGenerations, models, images } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

// GET 获取用户所有图片生成记录
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 获取用户ID
    const userId = session.user.id;

    // 从数据库获取用户的所有图片生成记录
    const generations = await database
      .select({
        id: imageGenerations.id,
        userId: imageGenerations.userId,
        userPrompt: imageGenerations.userPrompt,
        prompt: imageGenerations.prompt,
        negativePrompt: imageGenerations.negativePrompt,
        taskId: imageGenerations.taskId,
        modelId: imageGenerations.modelId,
        parameters: imageGenerations.parameters,
        status: imageGenerations.status,
        createdAt: imageGenerations.createdAt,
        completedAt: imageGenerations.completedAt,
        creditsUsed: imageGenerations.creditsUsed,
        metadata: imageGenerations.metadata,
        model: models,
        images: images
      })
      .from(imageGenerations)
      .leftJoin(models, eq(imageGenerations.modelId, models.id))
      .leftJoin(images, eq(imageGenerations.id, images.generationId))
      .where(eq(imageGenerations.userId, userId))
      .orderBy(desc(imageGenerations.createdAt));

    return NextResponse.json({ generations });
  } catch (error) {
    console.error('获取用户图片生成记录失败:', error);
    return NextResponse.json({ error: '获取图片生成记录失败' }, { status: 500 });
  }
}
