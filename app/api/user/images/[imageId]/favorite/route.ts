import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { database } from '@/lib/db/index';
import { images } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ imageId: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const userId = session.user.id;
    const { imageId } = await params;

    // 检查图片是否存在且属于该用户
    const imageResult = await database.select().from(images).where(and(eq(images.id, imageId), eq(images.userId, userId))).limit(1);
    const image = imageResult[0];

    if (!image) {
      return NextResponse.json({ error: '图片不存在或无权操作' }, { status: 404 });
    }

    // 切换收藏状态
    const newFavoriteStatus = !image.isFavorite;
    await database.update(images)
      .set({ isFavorite: newFavoriteStatus })
      .where(eq(images.id, imageId));

    return NextResponse.json({
      success: true,
      isFavorite: newFavoriteStatus
    });
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    return NextResponse.json({ error: '操作失败' }, { status: 500 });
  }
}
