import { auth } from '@/auth';
import { redirect } from 'next/navigation';

export default async function TestPayPalPage() {
  const session = await auth();
  
  if (!session?.user) {
    redirect('/login');
  }

  // PayPal client ID from environment variables
  const paypalClientId = process.env.NODE_ENV === 'production'
    ? process.env.PAYPAL_CLIENT_ID!
    : process.env.PAYPAL_SANDBOX_CLIENT_ID!;

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">PayPal Integration Test</h1>
          <p className="text-gray-600 mt-2">
            Test PayPal subscription functionality
          </p>
        </div>

        <div className="grid gap-6">
          {/* Environment Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">Environment Information</h3>
            <div className="text-sm text-blue-800">
              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
              <p><strong>PayPal Client ID:</strong> {paypalClientId ? 'Configured' : 'Not configured'}</p>
              <p><strong>User ID:</strong> {session.user.id}</p>
              <p><strong>User Email:</strong> {session.user.email}</p>
            </div>
          </div>

          {/* Test Actions */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Test Actions</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-2">1. Test Subscription Creation</h4>
                <button 
                  onClick={() => {
                    fetch('/api/paypal/subscription/create', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ planType: 'PREMIUM' })
                    })
                    .then(res => res.json())
                    .then(data => {
                      console.log('Subscription creation result:', data);
                      if (data.approvalUrl) {
                        window.open(data.approvalUrl, '_blank');
                      }
                    })
                    .catch(err => console.error('Error:', err));
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                  Create Premium Subscription
                </button>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-2">2. Test Subscription Management</h4>
                <button 
                  onClick={() => {
                    fetch('/api/paypal/subscription/manage')
                    .then(res => res.json())
                    .then(data => {
                      console.log('Subscription details:', data);
                      alert(JSON.stringify(data, null, 2));
                    })
                    .catch(err => console.error('Error:', err));
                  }}
                  className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                >
                  Get Subscription Details
                </button>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-2">3. Navigate to Subscription Page</h4>
                <a 
                  href="/subscription"
                  className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 inline-block"
                >
                  Go to Subscription Page
                </a>
              </div>
            </div>
          </div>

          {/* Configuration Checklist */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-900 mb-2">Configuration Checklist</h3>
            <div className="text-sm text-yellow-800 space-y-1">
              <p>✅ PayPal SDK installed</p>
              <p>✅ Database schema updated</p>
              <p>✅ API routes created</p>
              <p>✅ React components created</p>
              <p>⚠️ Environment variables need to be configured</p>
              <p>⚠️ PayPal product needs to be created</p>
              <p>⚠️ Webhooks need to be configured</p>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Next Steps</h3>
            <div className="text-sm text-gray-700 space-y-1">
              <p>1. Configure PayPal environment variables in .env.local</p>
              <p>2. Create PayPal product in developer console</p>
              <p>3. Set up webhooks for subscription events</p>
              <p>4. Run database migration: <code className="bg-gray-200 px-1 rounded">pnpm db:migrate</code></p>
              <p>5. Test with PayPal sandbox accounts</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
