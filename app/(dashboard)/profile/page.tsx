'use server'
import { redirect } from "next/navigation";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Label } from "../../../components/ui/label";
import { database } from "../../../lib/db/index";
import { users, subscriptions, imageGenerations, images } from "../../../lib/db/schema";
import { eq, and, count } from "drizzle-orm";
import { Badge } from "../../../components/ui/badge";
import UserAvatar from "../../../components/profile/user-avatar";
import ChangePasswordForm from "../../../components/profile/change-password-form";
import { auth } from "@/auth";
import DeleteAccountButton from "./delete-account";


export default async function ProfilePage() {
    const session = await auth()
    if (!session) {
        return redirect("/sign-in");
    }
    
    const userResult = await database.select().from(users).where(eq(users.email, session.user?.email as string)).limit(1);
    const user = userResult[0];

    if (!user) {
        return <><div>{session.user?.email}</div></>;
    }
    
    const subscriptionResult = await database.select().from(subscriptions).where(eq(subscriptions.userId, user.id)).limit(1);
    const subscription = subscriptionResult[0];
    const generationsCountResult = await database.select({ count: count() }).from(imageGenerations).where(eq(imageGenerations.userId, user.id));
    const generationsCount = generationsCountResult[0].count;
    const favoritesCountResult = await database
        .select({ count: count() })
        .from(images)
        .innerJoin(imageGenerations, eq(images.generationId, imageGenerations.id))
        .where(and(eq(imageGenerations.userId, user.id), eq(images.isFavorite, true)));
    const favoritesCount = favoritesCountResult[0].count;
    const formattedJoinDate = new Date(user.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }); 
    const subscriptionStatus = subscription?.status || "INACTIVE";
    const subscriptionType = subscription?.type || "FREE";
    const creditsRemaining = subscription?.creditsRemaining || 0;

    return (
        <div className="w-full max-w-7xl mt-16 mx-auto p-4 md:p-6 animate-in fade-in duration-500">
            {/* Header Section */}
            <div className="mb-8 text-center">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">User Profile</h1>
                <p className="text-gray-500 mt-2">Manage your account settings and preferences</p>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Column - User Info & Stats */}
                <div className="lg:col-span-1 space-y-6">
                    {/* User Card */}
                    <div className="p-5 border border-gray-200">
                        <div className="flex flex-col items-center">
                            <UserAvatar
                                avatarUrl={user.image || undefined}
                                email={user.email || ""}
                                size="lg"
                                showUploadOverlay={true}
                            />
                            <h2 className="text-xl font-bold text-gray-900">{user.name || "No Name Set"}</h2>
                            <p className="text-gray-500 text-sm mt-1">{user.email}</p>
                            <p className="text-xs text-gray-400 mt-2">Member since {formattedJoinDate}</p>
                            
                            <div className="flex flex-wrap gap-2 mt-4 justify-center">
                                <Badge variant={subscriptionType === "PREMIUM" ? "default" : "outline"} className="capitalize">
                                    {subscriptionType === "PREMIUM" ? "Premium" : "Free User"}
                                </Badge>
                                <Badge variant={subscriptionStatus === "ACTIVE" ? "default" : "outline"}>
                                    {subscriptionStatus === "ACTIVE" ? "Active" : "Inactive"}
                                </Badge>
                            </div>
                        </div>
                    </div>

                    {/* Stats Card */}
                    <div className="p-5 border border-gray-200">
                        <h3 className="text-lg font-semibold mb-4">Usage Statistics</h3>
                        <div className="space-y-4">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Generations</span>
                                <span className="font-bold text-primary">{generationsCount}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Favorites</span>
                                <span className="font-bold text-primary">{favoritesCount}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Credits</span>
                                <span className="font-bold text-primary">{creditsRemaining}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Column - Forms & Settings */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Profile Form Card */}
                    <div className="p-5 border border-gray-200">
                        <h3 className="text-lg font-semibold mb-4">Profile Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="name">Full Name</Label>
                                <Input
                                    id="name"
                                    defaultValue={user.name || ""}
                                    placeholder="Your full name"
                                    className="mt-1"
                                />
                            </div>
                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    defaultValue={user.email ?? ""}
                                    readOnly
                                    className="mt-1 bg-gray-50"
                                />
                                <p className="text-xs text-gray-400 mt-1">Email cannot be changed</p>
                            </div>
                            <div>
                                <Label htmlFor="joinDate">Join Date</Label>
                                <Input
                                    id="joinDate"
                                    defaultValue={formattedJoinDate}
                                    readOnly
                                    className="mt-1 bg-gray-50"
                                />
                            </div>
                        </div>
                        <div className="flex justify-end mt-6">
                            <Button>Save Changes</Button>
                        </div>
                    </div>

                    {/* Subscription Card */}
                    <div className="p-5 border border-gray-200">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                            <div>
                                <h3 className="text-lg font-semibold mb-1">Subscription Plan</h3>
                                <p className="text-gray-600">
                                    {subscriptionType === "PREMIUM"
                                        ? "Premium Plan - Full access to all features"
                                        : "Free Plan - Limited access to features"}
                                </p>
                                <p className="text-sm text-gray-500 mt-2">
                                    Status: <span className={subscriptionStatus === "ACTIVE" ? "text-green-600" : "text-gray-600"}>
                                        {subscriptionStatus === "ACTIVE" ? "Active" : "Inactive"}
                                    </span>
                                </p>
                            </div>
                            <Button
                                variant={subscriptionType === "PREMIUM" ? "outline" : "default"}
                                className="mt-2 sm:mt-0"
                            >
                                {subscriptionType === "PREMIUM" ? "Manage Subscription" : "Upgrade to Premium"}
                            </Button>
                        </div>
                    </div>

                    {/* Account Settings Card */}
                    <div className="p-5 border border-gray-200">
                        <h3 className="text-lg font-semibold mb-4">Account Security</h3>
                        <ChangePasswordForm />
                    </div>

                    {/* Danger Zone Card */}
                    <div className="p-5 border border-red-200 bg-red-50">
                        <h3 className="text-lg font-semibold text-red-700 mb-3">Danger Zone</h3>
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                            <div>
                                <p className="font-medium text-red-700">Delete Account</p>
                                <p className="text-sm text-gray-600">Permanently delete your account and all associated data.</p>
                            </div>
                            <DeleteAccountButton email={user.email || ""} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}


