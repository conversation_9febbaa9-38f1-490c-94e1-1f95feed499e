'use client'

import { RegisterForm } from "@/components/RegisterForm"
import { <PERSON>alog, DialogContent, DialogOverlay, DialogHeader, DialogTitle } from "@/components/ui/dialog" // DialogClose removed if not used for a button
import { useState } from "react"
import { useRouter } from "next/navigation"

export default function SignUpPage() {
    const [open, setOpen] = useState(true)
    const router = useRouter()

    const handleOpenChange = (isOpen: boolean) => {
        setOpen(isOpen)
        if (!isOpen) {
            router.back()
        }
    }

    return (
       <>
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent>
                <DialogHeader className="flex items-center justify-between">
                    <DialogTitle>Sign Up</DialogTitle>
                </DialogHeader>
            <RegisterForm /> 
            </DialogContent>
        </Dialog>
        </>
    )
}