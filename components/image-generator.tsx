'use client'
import { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion' // 1. 引入 framer-motion
import { ChevronDown, ChevronUp, Download, ImageIcon, Loader2, Maximize2, RefreshCw, X } from 'lucide-react'
import { <PERSON><PERSON> } from './ui/button'
import { Textarea } from './ui/textarea'
import { Input } from './ui/input'
import { Slider } from './ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { toast } from 'sonner'
import Image from 'next/image'
import { DialogHeader, DialogTitle, DialogContent, Dialog } from './ui/dialog'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

interface Model {
  id: string;
  modelName: string;
  description: string | null;
  creditCost: number;
}

// Define aspect ratio presets
const aspectRatioPresets = [
  { label: '1:1', value: '1:1', width: 512, height: 512 },
  { label: '2:3', value: '2:3', width: 512, height: 768 },
  { label: '3:4', value: '3:4', width: 768, height: 1024 },
  { label: '9:16 (Mobile)', value: '9:16', width: 576, height: 1024 },
  { label: '3:2', value: '3:2', width: 768, height: 512 },
  { label: '4:3', value: '4:3', width: 1024, height: 768 },
  { label: '16:9 (HD)', value: '16:9', width: 1024, height: 576 },
  { label: 'Custom', value: 'custom', width: '', height: '' }
];

export default function ImageGenerator({userCredits, models}: {userCredits: number, models: Model[]}) {
  const router = useRouter()
  const [isMounted, setIsMounted] = useState(false); // 2. 添加 isMounted 状态
  //state
  const [prompt, setPrompt] = useState('')
  const [num, setNum] = useState(1)
  const [negativePrompt, setNegativePrompt] = useState('')
  const [width, setWidth] = useState(aspectRatioPresets[0].width)
  const [height, setHeight] = useState(aspectRatioPresets[0].height)
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<string>(aspectRatioPresets[0].value)
  const [isCustomSize, setIsCustomSize] = useState<boolean>(aspectRatioPresets[0].value === 'custom')
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)
  const [seed, setSeed] = useState<number>(-1)
  const [steps, setSteps] = useState(10)
  const [guidance, setGuidance] = useState(7.5)
  const [selectedModel, setSelectedModel] = useState('')
  const [loading, setLoading] = useState(false)
  const [generatedImages, setGeneratedImages] = useState<string[]>([
    // {
    //   id: '1',
    //   url: 'https://v3.fal.media/files/lion/HFM6PXrHMY6EdLS0ey-Se.png',
    //   thumbnail_url: null,
    //   isFavorite: false,
    //   isLoading: false
    // }
  ])
  const [modelCredit, setModelCredit] = useState(0)
  const [creditCost, setCreditCost] = useState(0)
  const [error, setError] = useState<string | null>(null)
  // State for the image modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImageForModal, setSelectedImageForModal] = useState<string | null>(null);

  //check auth
  const session = useSession(); 

  useEffect(() => {
    setIsMounted(true); // 组件挂载后，设置 isMounted 为 true 以触发动画
  }, []);
    
  // Generate random seed
  const generateRandomSeed = useCallback(() => {
    setSeed(Math.floor(Math.random() * 2147483647))
  }, [])

  // Handle aspect ratio changes
  const handleAspectRatioChange = useCallback((value: string) => {
    setSelectedAspectRatio(value)
    const preset = aspectRatioPresets.find(p => p.value === value)
    if (preset) {
      if (value === 'custom') {
        setIsCustomSize(true)
        // Keep current dimensions when switching to custom
      } else {
        setWidth(preset.width)
        setHeight(preset.height)
        setIsCustomSize(false)
      }
    }
  }, [])

  // Handle manual dimension input
  const handleDimensionChange = useCallback((dimension: 'width' | 'height', value: string) => {
    const numericValue = parseInt(value, 10)
    if (isNaN(numericValue) || numericValue <= 0) return // Basic validation

    if (dimension === 'width') {
      setWidth(numericValue)
    } else {
      setHeight(numericValue)
    }
    
    // Only change to custom if not already in custom mode
    if (selectedAspectRatio !== 'custom') {
      setSelectedAspectRatio('custom')
      setIsCustomSize(true)
    }
  }, [selectedAspectRatio])
  
  // Toggle advanced settings
  const toggleAdvancedSettings = useCallback(() => {
    setShowAdvancedSettings(prev => !prev)
  }, [])

  // Handle model selection
  const handleModelChange = useCallback((modelId: string) => {
    setSelectedModel(modelId)
    const selectedModelData = models.find(m => m.id === modelId)
    if (selectedModelData) {
      setModelCredit(selectedModelData.creditCost)
      setCreditCost(num * selectedModelData.creditCost)
    } else {
      setModelCredit(0)
      setCreditCost(0)
      // Credits will be updated in the useEffect that depends on modelCredit
    }
  }, [models, num])

  // Generate image function
  const generateImage = useCallback(async () => {
    //check auth
    if(session?.status !== "authenticated") {
      // Trigger the @signin parallel route to show the login modal
      router.push('/sign-in');
      // Optionally show a toast, but the modal should be the primary indicator
      // toast('Error', {
      //   description: 'Please sign in to generate images',
      // });
      return;
    }
    // Validate inputs
    if (!prompt.trim()) {
      toast('Error', {
        description: 'Please enter a prompt',
      })
      return
    }
    
    if (!selectedModel) {
      toast('Error', {
        description: 'Please select a model',
      })
      return
    }

    try {
      setError(null)
      
      // Start loading state and create placeholders
      setLoading(true)

      // Make API request to generate images
      const result = await fetch('/api/model/queue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt,
          negative_prompt: negativePrompt,
          parameters: {
            width: Number(width) || Number(aspectRatioPresets[0].width), // Ensure number
            height: Number(height) || Number(aspectRatioPresets[0].height), // Ensure number
            steps,
            guidance,
            seed: seed === -1 ? undefined : seed, // Handle -1 as undefined for random seed
            num
          },
          model_id: selectedModel
        })
      })
      const body = await result.json();
      
      if (body.status !== 'ok') {
        throw new Error(`Queue failed: ${body.error}`)
      }
      
      const { taskId, credits } = body
      if (!taskId || !credits) {
        throw new Error('Failed to generate image: Invalid response')
      }
      
      // Poll for task completion
      const clearIntervalId: ReturnType<typeof setInterval> = setInterval(async () => {
        const taskStatus = await queryTask(taskId)
        if (taskStatus) {
          clearInterval(clearIntervalId)
          setLoading(false)
        }
      }, 3000)
      
      toast.success('Success', {
        description: 'Image generation started!',
        duration: 5000,
        //action: { label: 'Try agent', onClick: ()=>generateImage()},
      })
      
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Failed to generate image. Please try again.';
      toast.error('Error',{
        description: message,
        duration: 5000,
        style:{color:'red'}
        //action: { label: 'Try agent', onClick: ()=>generateImage()},
      })
      // Clear loading placeholders on error
      setGeneratedImages([])
      setLoading(false)
    } finally {
    
    }
  }, [prompt, negativePrompt, width, height, steps, guidance, seed, num, selectedModel])

  // Query task status
  const queryTask = async (taskId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/model/task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ taskId })
      })
      
      if (!response.ok) throw new Error('Failed to fetch task status')
      
      const data = await response.json()
      if(data.status === "ok"){
        setGeneratedImages(data.images)
        return true
      } 
      return false
    } catch (error) {
      return false // Return false instead of showing toast to avoid spamming toasts
    }
  }

  // Download image
  const handleDownload = async (imageUrl: string, fileName: string) => {
    try {
      setError(null)
      
      // Try to fetch the image directly
      const response = await fetch(imageUrl)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`)
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
      
      toast('Download Started', {
        description: `Image "${fileName}" is downloading.`
      })
    } catch (error: unknown) {
      
      // If direct download fails, offer alternative
      if (error instanceof Error && error.message.includes('CORS')) {
        // Fallback for CORS issues - open image in new tab
        window.open(imageUrl, '_blank')
        toast('CORS Issue Detected', {
          description: 'Image opened in new tab instead. You can right-click and save it from there.',
        })
      } else {
        toast('Download Error', {
          description: error instanceof Error ? error.message : 'Failed to download image.',
          })
      }
    }
  }

  // Calculate display-specific variables before the return statement
  const imagesToDisplay = generatedImages.slice(0, 4)
  //const numImagesToDisplay = imagesToDisplay.length
  // 动态计算当前图片比例 - aspectRatio is removed as it's not used

  // Function to open the modal
  const handleOpenImageModal = (imageUrl: string) => {
    setSelectedImageForModal(imageUrl);
    setIsModalOpen(true);
  };
  
  // Update credits calculation when num or modelCredit changes
  useEffect(() => {
    const cost = num * modelCredit
    //setUserAvailableCredits(userAvailableCredits - cost)
    setCreditCost(cost)
  }, [num, modelCredit])

  // Initialize with random seed
  useEffect(() => {
    generateRandomSeed()
  }, [generateRandomSeed])
  
  // Effect to initialize and sync aspect ratio with width/height
  useEffect(() => {
    const currentPreset = aspectRatioPresets.find(p => p.value === selectedAspectRatio)
    if (currentPreset) {
      if (selectedAspectRatio === 'custom') {
        setIsCustomSize(true)
      } else {
        // If a preset is selected, enforce its dimensions
        if (width !== currentPreset.width || height !== currentPreset.height) {
          setWidth(currentPreset.width)
          setHeight(currentPreset.height)
        }
        setIsCustomSize(false)
      }
    }
  }, [selectedAspectRatio, width, height])

  const imageDisplayWidth = Number(width) || Number(aspectRatioPresets[0].width) || 512;
  const imageDisplayHeight = Number(height) || Number(aspectRatioPresets[0].height) || 512;


  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }} 
        animate={{ opacity: isMounted ? 1 : 0, y: isMounted ? 0 : 20 }} 
        transition={{ duration: 0.6, ease: "easeOut" }} 
        className="max-w-6xl mx-auto px-4 py-6 md:py-10 min-h-[calc(100vh-4rem)]"
      >
        <div className="w-full flex flex-row flex-wrap gap-6">
          {/* 左侧参数表单 */}
          <div className="w-full sm:w-[300px] md:w-[400px]">
            <div className="bg-white rounded-sm shadow-lg p-6 flex flex-col gap-6 border border-gray-100">
              <div className="space-y-4">
                <div>
                  <Select
                    value={selectedModel}
                    onValueChange={handleModelChange}
                  >
                    <SelectTrigger className="cursor-pointer">
                      <SelectValue placeholder="Select Model" />
                    </SelectTrigger>
                    <SelectContent>
                      {models.map((model) => (
                        <SelectItem key={model.id} value={model.id} className="cursor-pointer">
                          {model.modelName} ({model.creditCost} credits)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Prompt</label>
                  <Textarea
                    placeholder="Describe the image you want to generate..."
                    value={prompt}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setPrompt(e.target.value)}
                    className="min-h-24 mt-1"
                  />
                </div>
                
                <div className='hidden'>
                  <label className="text-sm font-medium">Negative Prompt</label>
                  <Textarea
                    placeholder="Elements to exclude from the image..."
                    value={negativePrompt}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setNegativePrompt(e.target.value)}
                    className="mt-1"
                  />
                </div>
                
                {/* <div>
                  <div className="flex justify-between items-center">
                    <label className="text-sm font-medium">Number of Images</label>
                    <span className="text-xs font-medium bg-gray-100 px-2 py-1 rounded-full">{num}</span>
                  </div>
                  <Slider
                    value={[num]}
                    min={1}
                    max={4}
                    step={1}
                    onValueChange={(vals: number[]) => setNum(vals[0])}
                    className="mt-3"
                  />
                </div> */}
                
                {/* Image Size */}
                <div>
                  <div className="flex flex-col">
                    <div>
                    <label className="text-sm font-medium mb-1 block">Image Size</label>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-end gap-2">
                        <div className="flex-grow-[2]">
                          <Select value={selectedAspectRatio} onValueChange={handleAspectRatioChange}>
                            <SelectTrigger className="cursor-pointer w-[140px]">
                              <SelectValue placeholder="Select Aspect Ratio" />
                            </SelectTrigger>
                            <SelectContent>
                              {aspectRatioPresets.map(preset => (
                                <SelectItem key={preset.value} value={preset.value} className="cursor-pointer">
                                  {preset.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        </div>
                        <div className="flex-grow">
                          <Input
                              type="number"
                              value={width}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleDimensionChange('width', e.target.value)}
                              readOnly={!isCustomSize}
                              className="w-full"
                              min={128}
                              step={8}
                              placeholder="Width"
                            />
                        </div>

                        <span className="text-gray-500 pb-2">x</span>

                        <div className="flex-grow">
                          <Input
                            type="number"
                            value={height}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleDimensionChange('height', e.target.value)}
                            readOnly={!isCustomSize}
                            className="w-full"
                            min={128}
                            step={8}
                            placeholder="Height"
                          />
                        </div>
                      </div>
                    </div>
                </div>
                
                {/* Advanced Settings */}
                <div className="mt-4 border border-gray-200 rounded-md bg-gray-50/50 overflow-hidden">
                  {/* Button as clickable header */}
                  <Button 
                    variant="ghost" 
                    onClick={toggleAdvancedSettings}
                    className="w-full flex justify-between items-center p-3 hover:bg-gray-100"
                  >
                    <span className="text-sm font-medium">Advanced Settings</span>
                    {showAdvancedSettings ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>

                  {showAdvancedSettings && (
                    <div className="p-4 space-y-4 border-t border-gray-200">
                      {/* Seed */}
                      <div className="flex space-x-2 mt-1">
                        <Input
                          type="number"
                          value={seed}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            const val = parseInt(e.target.value, 10)
                            setSeed(isNaN(val) ? -1 : val) // Allow empty or invalid to reset to -1
                          }}
                          placeholder="随机种子 (-1 为随机)"
                          className="flex-grow"
                        />
                        <Button variant="outline" onClick={generateRandomSeed} className="shrink-0">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                      {/* Steps */}
                      <div>
                        <div className="flex justify-between items-center">
                          <label className="text-sm font-medium">Steps</label>
                          <span className="text-xs font-medium bg-gray-100 px-2 py-1 rounded-full">{steps}</span>
                        </div>
                        <Slider
                          value={[steps]}
                          min={1}
                          max={50}
                          step={1}
                          onValueChange={(vals: number[]) => setSteps(vals[0])}
                          className="mt-2"
                        />
                      </div>
                      {/* Guidance Scale */}
                      <div>
                        <div className="flex justify-between items-center">
                          <label className="text-sm font-medium">Guidance Scale</label>
                          <span className="text-xs font-medium bg-gray-100 px-2 py-1 rounded-full">{guidance.toFixed(1)}</span>
                        </div>
                        <Slider
                          value={[guidance]}
                          min={1}
                          max={20}
                          step={0.1}
                          onValueChange={(vals: number[]) => setGuidance(vals[0])}
                          className="mt-2"
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div>            
                  <Button 
                  onClick={generateImage} 
                  disabled={ loading }
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-300 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    `Generate Image (${creditCost} credits)`
                  )}
                </Button>
                {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
              </div>
            </div>
          </div>
        </div>

          {/* Right Image Display Area */}
          <div className="w-full sm:w-[380px] sm:h-[380px] md:w-[640px] md:h-[640px] border border-gray-100">
            <div className="h-full flex flex-row flex-wrap gap-4">
              {/* Image Grid or Placeholder */}
              {imagesToDisplay.length === 0 ? (
                <div className="flex-grow flex flex-col items-center justify-center text-gray-400">
                  <ImageIcon size={64} className="mb-4" />
                  <p className="text-lg">Your image will appear here</p>
                  <p className="text-sm">Enter your prompt and click "Generate Image" to start</p>
                </div>
              ) : (             
                    imagesToDisplay.map((image, index) => (
                    <div  
                      key={`loading-${index}`} 
                      className="relative w-full h-full overflow-hidden shadow-md flex items-center justify-center"
                    >
                      {loading && (
                        <>
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Generating, please wait...
                        </>
                      )}
                      {!loading && (
                        <>
                          <Image
                            src={image}
                            alt={`Generated image ${index + 1}`}
                            width={imageDisplayWidth}
                            height={imageDisplayHeight}
                            className="object-contain w-full h-full transition-transform duration-300 group-hover:scale-105"
                            priority={index < 2}
                          />
                          <div className="absolute z-10 gap-2 bottom-1 right-1 transition-opacity duration-300 flex items-center justify-center">
                          <Button 
                              variant="ghost" 
                              size="icon" 
                              className="cursor-pointer text-gray-600 bg-gray-100 hover:text-gray-600 hover:bg-gray-200"
                              onClick={() => handleOpenImageModal(image as string)}
                              title="View Full Size"
                            >
                              <Maximize2 className="h-6 w-6" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="cursor-pointer text-gray-600 bg-gray-100 hover:text-gray-600 hover:bg-gray-200"
                              onClick={() => handleDownload(image, `generated_image_${index}.png`)}
                              title="Download Image"
                            >
                              <Download className="h-6 w-6" />
                            </Button>
                          </div>
                        </>
                      )}
                    </div>
                  ))
              )}
            </div>
          </div>
        </div>
      </motion.div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogHeader>
          <DialogTitle></DialogTitle>
        </DialogHeader>
        <DialogContent className="sm:max-w-[1024px] max-h-[900px] p-0 border-none bg-transparent shadow-none">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {selectedImageForModal && (
              <Image 
                src={selectedImageForModal}
                alt="Full size generated image"
                width={1280}
                height={1024}
                className="object-contain"
                priority
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
    )
}
