'use client'
import Link from "next/link"
import { But<PERSON> } from "./ui/button"
import { ChevronRight } from "lucide-react"
import Image from "next/image"
import UserAvatar from "./profile/user-avatar"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Menu, User } from "lucide-react";
import {
  She<PERSON>,
  Sheet<PERSON>rigger,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "./ui/sheet"
import { useState } from "react"
import { useSession, signOut } from "next-auth/react"
import { LoginForm } from "./LoginForm"


export default function Nav() {
    const { data: session } = useSession()
    // 控制移动端 Sheet 打开状态
    const [open, setOpen] = useState(false)

    return (
        <header className="sticky top-0 z-50 max-w-7xl mt-0 sm:mt-4 mx-auto bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-16 items-center justify-between px-2 md:px-0">
          <div className="flex items-center gap-2 w-48 font-bold">
            
            <Link href="/" className="text-gray-800 font-bold text-lg transition-colors hover:text-foreground"><span>Photo AI</span></Link>
          </div>
          {/* 桌面端菜单 */}
          <nav className="hidden md:flex gap-8 border border-gray-500 rounded-full px-12 py-4">
            <Link
                href="/text-image-generator"
                className="text-black font-medium hover:text-gray-600"
              >
                Text to Image Generator
              </Link>
            <Link
              href="#faq"
              className="text-black font-medium hover:text-gray-600"
            >
              FAQ
            </Link>
          </nav>
          {/* 移动端汉堡菜单 */}
          <div className="flex md:hidden items-center">
            <Sheet open={open} onOpenChange={setOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="mr-2">
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Open Menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left">
                <SheetHeader>
                  <SheetTitle>
                    <div className="flex items-center gap-2 font-bold">
                      <Image src="/logo.png" alt="Photo AI" width={24} height={24} />
                      <span>Photo AI</span>
                    </div>
                  </SheetTitle>
                  <SheetDescription>
                    Qick Access
                  </SheetDescription>
                </SheetHeader>
                <div className="flex flex-col gap-4 mt-6">
                  <Link href="/text-image-generator" className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground py-2" onClick={() => setOpen(false)}>Text to Image Generator</Link>
                  <Link href="#faq" className="text-base font-medium text-muted-foreground transition-colors hover:text-foreground py-2" onClick={() => setOpen(false)}>FAQ</Link>
                </div>
                <div className="mt-8 border-t pt-4">
                  {session ? (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <span className="cursor-pointer flex items-center gap-2">
                          <User className="h-6 w-6" />
                          <span className="font-medium text-sm">{session?.user?.email}</span>
                        </span>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <DropdownMenuLabel>My Account</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href="/profile" onClick={() => setOpen(false)}>Profile</Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/settings" onClick={() => setOpen(false)}>Settings</Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href="/my-images" onClick={() => setOpen(false)}>My Images</Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/subscription" onClick={() => setOpen(false)}>Subscription</Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href="#" onClick={() => {signOut({redirect:true, redirectTo:'/'}); setOpen(false)}}>Sign Out</Link>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <Link href="/sign-in">Sign In</Link>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
          {/* 桌面端用户菜单/登录按钮 */}
          <div className="hidden md:flex items-center justify-center gap-4 w-64">
            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <span className="cursor-pointer">
                    <User className="h-6 w-6" />
                  </span>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile">Profile</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings">Settings</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/my-images">My Images</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/subscription">Subscription</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <a href="#" onClick={() => {signOut({redirect:true, redirectTo:'/'}); setOpen(false)}}>Sign Out</a>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link href="/sign-in">Sign In</Link>
            )}
          </div>
        </div>
        </header>
    )
}