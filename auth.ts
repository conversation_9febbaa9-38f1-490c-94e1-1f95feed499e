import NextAuth from "next-auth";
import Cred<PERSON>sProvider from "next-auth/providers/credentials";
import { database } from "./lib/db/index";
import { users, subscriptions, accounts, models } from "./lib/db/schema";
import { compare } from "bcryptjs";
import Github from "next-auth/providers/github";
import { verifyTurnstile } from "./lib/cf/turnstile";
import { eq, and } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export const { handlers, auth, signIn, signOut} = NextAuth({
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                token: { label: "Token", type: "hidden" },
            },
            async authorize(credentials) {

                if (!credentials?.email || !credentials?.password || !credentials?.token) {
                    return null
                }
                //check turnstile token
                
                try{
                    const isTurnstileValid = await verifyTurnstile(credentials?.token as string)
                    if (!isTurnstileValid) {
                        return null
                    }
                    const userResult = await database.select().from(users).where(eq(users.email, credentials?.email as string)).limit(1);
                    const user = userResult[0];
                    if (!user) {
                        return null
                    }
                    const isPasswordValid = await compare(credentials?.password as string, user.passwordHash as string);
                    if (!isPasswordValid) {
                        return null
                    }
                    return user;
                }catch(error){
                    return null
                }
            },
        }),
        Github({
            clientId: process.env.AUTH_GITHUB_ID,
            clientSecret: process.env.AUTH_GITHUB_SECRET,
        })
    ],
    callbacks:{
        signIn: async ({user, account, profile, credentials, email}) => {
            try {
                //check user exist
                const userExistResult = await database.select().from(users).where(eq(users.email, user.email as string)).limit(1);
                const userExist = userExistResult[0];

                let userSubscription = null;
                if (userExist) {
                    const subscriptionResult = await database.select().from(subscriptions).where(eq(subscriptions.userId, userExist.id)).limit(1);
                    userSubscription = subscriptionResult[0];
                }

                let isNewUser = false;
                let newUser;
                if (!userExist && account?.type === "oauth") {
                    const newUserId = createId();
                    await database.insert(users).values({
                        id: newUserId,
                        email: user.email as string,
                        name: profile?.name as string,
                        image: profile?.image as string,
                    });

                    await database.insert(accounts).values({
                        userId: newUserId,
                        type: account?.type as string,
                        provider: account?.provider as string,
                        providerAccountId: account?.providerAccountId as string,
                        access_token: account?.access_token as string,
                        refresh_token: account?.refresh_token as string,
                        expires_at: account?.expires_at as number,
                        token_type: account?.token_type as string,
                        scope: account?.scope as string,
                        id_token: account?.id_token as string,
                        session_state: account?.session_state as string,
                    });

                    newUser = { id: newUserId };
                    isNewUser = true;
                }
                const userId = isNewUser ? newUser?.id : userExist?.id;
                //如果是email注册第一次登陆或者新的oauth用户没有订阅，就创建一个
                if (isNewUser || (userExist && !userSubscription)) { // Corrected logic for existing user without subscription
                    await database.insert(subscriptions).values({
                        id: createId(),
                        userId: userId!,
                        type: "FREE",
                        status: "ACTIVE",
                        creditsGrantedPerMonth: 30,
                        creditsRemaining: 30,
                    });
                } else if (userExist) { // Ensure userExist before trying to update
                    //update user updated
                    await database.update(users)
                        .set({ updatedAt: new Date() })
                        .where(eq(users.email, user.email as string));
                }
                return true;
            } catch (error) {
                return false; // Explicitly return false on error to prevent login
            }
        },
        //end
        jwt: async ({ token, user }) => {
            if (user) {
                //find user by email
                const userExistResult = await database.select().from(users).where(eq(users.email, user.email as string)).limit(1);
                const userExist = userExistResult[0];
                token.id = userExist?.id as string;
                token.email = user.email as string;
                token.name = user.name as string;
                token.image = user.image as string;
            }
            return token;
        },
        session: async ({ session, token }) => { 
            if (session.user && token.id) { 
                session.user.id = token.id as string; 
                session.user.email = token.email as string;
                session.user.name = token.name as string;
                session.user.image = token.image as string;
                
                // 获取用户积分和模型信息
                const userSubscriptionResult = await database.select().from(subscriptions).where(eq(subscriptions.userId, token.id as string)).limit(1);
                const userSubscription = userSubscriptionResult[0];

                if (userSubscription) {
                    session.user.credits = userSubscription.creditsRemaining;
                }

                // 获取可用的模型列表
                const modelsList = await database.select().from(models).where(and(eq(models.category, "TEXT_TO_IMAGE"), eq(models.isAvailable, true)));

                session.user.models = modelsList;
            }
            return session;
        },
    }
})