import { database } from '../lib/db/index';
import { models, subscriptions, users } from '../lib/db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { createId } from '@paralleldrive/cuid2';

const modelSeedData = [
  {
    id: createId(),
    modelName: "Flux/Schnell",
    modelId: "fal-ai/flux/schnell",
    category: "TEXT_TO_IMAGE" as const,
    provider: "Fal AI",
    description: "Powerful open-source text-to-image model capable of generating highly detailed images.",
    isAvailable: true,
    creditCost: 1,
    parameters: {
      default_width: 1024,
      default_height: 1024,
      supported_resolutions: ["512x512", "768x768", "1024x1024"],
      negative_prompt_support: true
    }
  },
  {
    id: createId(),
    modelName: "Flux/Dev",
    modelId: "fal-ai/flux/dev",
    category: "TEXT_TO_IMAGE" as const,
    provider: "Fal AI",
    description: "Advanced text-to-image model with enhanced quality and detail generation capabilities.",
    isAvailable: true,
    creditCost: 2,
    parameters: {
      default_width: 1024,
      default_height: 1024,
      supported_resolutions: ["512x512", "768x768", "1024x1024", "1536x1536"],
      negative_prompt_support: true,
      guidance_scale_range: [1, 20]
    }
  },
  {
    id: createId(),
    modelName: "Flux/Pro",
    modelId: "fal-ai/flux-pro",
    category: "TEXT_TO_IMAGE" as const,
    provider: "Fal AI",
    description: "Professional-grade text-to-image model with superior quality and commercial licensing.",
    isAvailable: true,
    creditCost: 5,
    parameters: {
      default_width: 1024,
      default_height: 1024,
      supported_resolutions: ["512x512", "768x768", "1024x1024", "1536x1536", "2048x2048"],
      negative_prompt_support: true,
      guidance_scale_range: [1, 30],
      steps_range: [10, 50]
    }
  }
];

async function seed() {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await database.delete(models);
    
    // Insert models
    console.log('📦 Inserting models...');
    await database.insert(models).values(modelSeedData);
    console.log(`✅ Inserted ${modelSeedData.length} models`);

    // Create a test user if it doesn't exist
    const testEmail = '<EMAIL>';
    const existingUser = await database.select().from(users).where(eq(users.email, testEmail)).limit(1);
    
    if (existingUser.length === 0) {
      console.log('👤 Creating test user...');
      const hashedPassword = await bcrypt.hash('password123', 10);
      const userId = createId();
      
      await database.insert(users).values({
        id: userId,
        email: testEmail,
        name: 'Test User',
        passwordHash: hashedPassword,
        isActive: true,
      });

      // Create subscription for test user
      await database.insert(subscriptions).values({
        id: createId(),
        userId: userId,
        type: 'FREE',
        status: 'ACTIVE',
        creditsGrantedPerMonth: 30,
        creditsRemaining: 30,
      });

      console.log('✅ Created test user with FREE subscription');
    } else {
      console.log('👤 Test user already exists, skipping...');
    }

    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

seed();
