CREATE TYPE "public"."generation_status" AS ENUM('PENDING', 'COMPLETED', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."model_category" AS ENUM('TEXT_TO_IMAGE', 'IMAGE_TO_IMAGE', 'IMAGE_ENHANCEMENT', 'IMAGE_TO_TEXT', 'TEXT_TO_VIDEO', 'IMAGE_TO_VIDEO', 'VIDEO_TO_VIDEO');--> statement-breakpoint
CREATE TYPE "public"."subscription_status" AS ENUM('ACTIVE', 'CANCELED', 'INACTIVE', 'PAST_DUE', 'TRIALING');--> statement-breakpoint
CREATE TYPE "public"."subscription_type" AS ENUM('FREE', 'PREMIUM');--> statement-breakpoint
CREATE TABLE "Account" (
	"userId" text NOT NULL,
	"type" text NOT NULL,
	"provider" text NOT NULL,
	"providerAccountId" text NOT NULL,
	"refresh_token" text,
	"access_token" text,
	"expires_at" integer,
	"token_type" text,
	"scope" text,
	"id_token" text,
	"session_state" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Account_provider_providerAccountId_unique" UNIQUE("provider","providerAccountId")
);
--> statement-breakpoint
CREATE TABLE "Authenticator" (
	"credentialID" text NOT NULL,
	"userId" text NOT NULL,
	"providerAccountId" text NOT NULL,
	"credentialPublicKey" text NOT NULL,
	"counter" integer NOT NULL,
	"credentialDeviceType" text NOT NULL,
	"credentialBackedUp" boolean NOT NULL,
	"transports" text,
	CONSTRAINT "Authenticator_credentialID_unique" UNIQUE("credentialID"),
	CONSTRAINT "Authenticator_userId_credentialID_unique" UNIQUE("userId","credentialID")
);
--> statement-breakpoint
CREATE TABLE "ImageGeneration" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"userPrompt" text NOT NULL,
	"prompt" text NOT NULL,
	"negativePrompt" text,
	"taskId" text,
	"modelId" text NOT NULL,
	"parameters" json NOT NULL,
	"status" "generation_status" DEFAULT 'PENDING' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"completedAt" timestamp,
	"creditsUsed" integer NOT NULL,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "Image" (
	"id" text PRIMARY KEY NOT NULL,
	"generationId" text NOT NULL,
	"userId" text NOT NULL,
	"imageUrl" text NOT NULL,
	"thumbnailUrl" text,
	"storagePath" text,
	"isFavorite" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Model" (
	"id" text PRIMARY KEY NOT NULL,
	"modelName" text NOT NULL,
	"modelId" text NOT NULL,
	"category" "model_category" NOT NULL,
	"provider" text NOT NULL,
	"description" text,
	"isAvailable" boolean DEFAULT true NOT NULL,
	"creditCost" integer NOT NULL,
	"parameters" json,
	CONSTRAINT "Model_modelId_unique" UNIQUE("modelId")
);
--> statement-breakpoint
CREATE TABLE "Session" (
	"sessionToken" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"expires" timestamp NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Subscription" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"type" "subscription_type" NOT NULL,
	"status" "subscription_status" DEFAULT 'INACTIVE' NOT NULL,
	"creditsGrantedPerMonth" integer DEFAULT 0 NOT NULL,
	"creditsRemaining" integer DEFAULT 0 NOT NULL,
	"currentPeriodStart" timestamp,
	"currentPeriodEnd" timestamp,
	"trialEndsAt" timestamp,
	"stripeSubscriptionId" text,
	"stripeCustomerId" text,
	"stripePaymentMethodId" text,
	"stripePriceId" text,
	"paypalSubscriptionId" text,
	"paypalCustomerId" text,
	"paypalPlanId" text,
	"paymentProvider" text DEFAULT 'stripe',
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Subscription_userId_unique" UNIQUE("userId"),
	CONSTRAINT "Subscription_stripeSubscriptionId_unique" UNIQUE("stripeSubscriptionId"),
	CONSTRAINT "Subscription_stripeCustomerId_unique" UNIQUE("stripeCustomerId"),
	CONSTRAINT "Subscription_paypalSubscriptionId_unique" UNIQUE("paypalSubscriptionId"),
	CONSTRAINT "Subscription_paypalCustomerId_unique" UNIQUE("paypalCustomerId")
);
--> statement-breakpoint
CREATE TABLE "User" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text,
	"email" text NOT NULL,
	"passwordHash" text,
	"emailVerified" timestamp,
	"image" text,
	"isActive" boolean DEFAULT true NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "User_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "VerificationToken" (
	"identifier" text NOT NULL,
	"token" text NOT NULL,
	"expires" timestamp NOT NULL,
	CONSTRAINT "VerificationToken_identifier_token_unique" UNIQUE("identifier","token")
);
